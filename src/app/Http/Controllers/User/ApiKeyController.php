<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ApiKeyController extends Controller
{
    /**
     * Display a listing of the user's API keys.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $apiKeys = $user->apiKeys()
            ->withCount('requestLogs')
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('User/ApiKeys/Index', [
            'apiKeys' => $apiKeys,
        ]);
    }

    /**
     * Show the form for creating a new API key.
     */
    public function create()
    {
        return Inertia::render('User/ApiKeys/Create');
    }

    /**
     * Store a newly created API key.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'expires_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = $request->user();

        // Check if user has reached API key limit (optional)
        $maxApiKeys = config('app.max_api_keys_per_user', 10);
        if ($user->apiKeys()->count() >= $maxApiKeys) {
            return back()->withErrors([
                'name' => "You have reached the maximum limit of {$maxApiKeys} API keys."
            ])->withInput();
        }

        // Generate a secure API key
        $keyValue = 'llmr_' . Str::random(40);
        
        $apiKey = ApiKey::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'key_hash' => hash('sha256', $keyValue),
            'key_prefix' => substr($keyValue, 0, 8) . '...',
            'expires_at' => $request->expires_at,
            'permissions' => [],
            'is_active' => true,
            'last_used_at' => null,
        ]);

        return redirect()->route('dashboard.api-keys.index')
            ->with('success', 'API key created successfully.')
            ->with('api_key_value', $keyValue); // Show only once
    }

    /**
     * Remove the specified API key.
     */
    public function destroy(Request $request, ApiKey $apiKey)
    {
        $user = $request->user();

        // Ensure user can only delete their own API keys
        if ($apiKey->user_id !== $user->id) {
            abort(403, 'You can only delete your own API keys.');
        }

        $apiKey->delete();

        return redirect()->route('dashboard.api-keys.index')
            ->with('success', 'API key deleted successfully.');
    }

    /**
     * Regenerate an API key.
     */
    public function regenerate(Request $request, ApiKey $apiKey)
    {
        $user = $request->user();

        // Ensure user can only regenerate their own API keys
        if ($apiKey->user_id !== $user->id) {
            abort(403, 'You can only regenerate your own API keys.');
        }

        // Generate new key
        $keyValue = 'llmr_' . Str::random(40);
        
        $apiKey->update([
            'key_hash' => hash('sha256', $keyValue),
            'key_prefix' => substr($keyValue, 0, 8) . '...',
            'last_used_at' => null,
        ]);

        return redirect()->route('dashboard.api-keys.index')
            ->with('success', 'API key regenerated successfully.')
            ->with('api_key_value', $keyValue); // Show only once
    }
}
