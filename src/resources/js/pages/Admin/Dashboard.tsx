import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
    BarChart3,
    Server,
    Cpu,
    Users,
    User,
    Key,
    ArrowRight
} from 'lucide-react';

export default function AdminDashboard() {
    const breadcrumbItems = [
        { name: 'Admin Dashboard', current: true },
    ];

    const quickAccessItems = [
        {
            title: 'Analytics',
            description: 'View usage statistics and performance metrics',
            href: '/admin/analytics',
            icon: BarChart3,
            color: 'text-blue-600',
        },
        {
            title: 'Providers',
            description: 'Manage LLM providers and API configurations',
            href: '/admin/providers',
            icon: Server,
            color: 'text-green-600',
        },
        {
            title: 'Models',
            description: 'Configure models, pricing, and capabilities',
            href: '/admin/models',
            icon: Cpu,
            color: 'text-purple-600',
        },
        {
            title: 'Groups',
            description: 'Manage user groups and permissions',
            href: '/admin/groups',
            icon: Users,
            color: 'text-indigo-600',
        },
        {
            title: 'Users',
            description: 'Manage users and group assignments',
            href: '/admin/users',
            icon: User,
            color: 'text-pink-600',
        },
        {
            title: 'API Keys',
            description: 'View and manage user API keys',
            href: '/admin/api-keys',
            icon: Key,
            color: 'text-orange-600',
        },
    ];

    const gettingStartedSteps = [
        {
            step: 1,
            title: 'Configure Providers',
            description: 'Add your LLM providers (OpenAI, Anthropic, etc.) and configure their API keys.',
        },
        {
            step: 2,
            title: 'Add Models',
            description: 'Configure the models available from your providers with pricing and capabilities.',
        },
        {
            step: 3,
            title: 'Create Groups',
            description: 'Set up user groups with specific model access permissions.',
        },
        {
            step: 4,
            title: 'Add Users',
            description: 'Create users and assign them to groups to control their access.',
        },
        {
            step: 5,
            title: 'Generate API Keys',
            description: 'Create API keys for users to access the LLM router endpoints.',
        },
    ];

    return (
        <AdminLayout
            header={
                <div>
                    <h1 className="text-2xl font-bold">Admin Dashboard</h1>
                    <p className="text-muted-foreground">
                        Manage your enterprise LLM routing, users, and API access
                    </p>
                </div>
            }
        >
            <Head title="Admin Dashboard" />

            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {/* Welcome Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Welcome to LLM Router Admin</CardTitle>
                        <CardDescription>
                            Manage your enterprise LLM routing, users, and API access from this central dashboard.
                        </CardDescription>
                    </CardHeader>
                </Card>

                {/* Quick Access Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Access</CardTitle>
                        <CardDescription>
                            Jump to the most commonly used admin functions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                            {quickAccessItems.map((item) => (
                                <Card key={item.title} className="hover:shadow-md transition-shadow">
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <item.icon className={`h-5 w-5 ${item.color}`} />
                                            <CardTitle className="text-base">{item.title}</CardTitle>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="pt-0">
                                        <CardDescription className="mb-4">
                                            {item.description}
                                        </CardDescription>
                                        <Button asChild size="sm" variant="outline" className="w-full">
                                            <Link href={item.href}>
                                                Go to {item.title}
                                                <ArrowRight className="ml-2 h-4 w-4" />
                                            </Link>
                                        </Button>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Getting Started Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Getting Started</CardTitle>
                        <CardDescription>
                            Follow these steps to set up your LLM router
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {gettingStartedSteps.map((step, index) => (
                                <div key={step.step} className="flex items-start gap-4 relative">
                                    <div className="flex-shrink-0">
                                        <div className="flex items-center justify-center h-8 w-8 rounded-full bg-primary text-primary-foreground text-sm font-medium">
                                            {step.step}
                                        </div>
                                    </div>
                                    <div className="flex-1">
                                        <h4 className="font-semibold">{step.title}</h4>
                                        <p className="text-sm text-muted-foreground mt-1">
                                            {step.description}
                                        </p>
                                    </div>
                                    {index < gettingStartedSteps.length - 1 && (
                                        <div className="absolute left-4 top-8 h-6 w-px bg-border" />
                                    )}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
