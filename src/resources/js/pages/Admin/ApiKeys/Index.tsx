import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  KeyIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  is_active: boolean;
  expires_at: string | null;
  last_used_at: string | null;
  created_at: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  request_logs_count: number;
}

interface ApiKeysIndexProps {
  apiKeys: ApiKey[];
}

export default function ApiKeysIndex({ apiKeys }: ApiKeysIndexProps) {
  const handleDelete = (apiKey: ApiKey) => {
    if (confirm(`Are you sure you want to revoke API key "${apiKey.name}"?`)) {
      router.delete(`/admin/api-keys/${apiKey.id}`, {
        onSuccess: () => {
          // Handle success if needed
        },
        onError: (errors) => {
          console.error('Delete failed:', errors);
        },
      });
    }
  };

  const handleToggleStatus = (apiKey: ApiKey) => {
    const action = apiKey.is_active ? 'revoke' : 'activate';
    const endpoint = `/admin/api-keys/${apiKey.id}/${action}`;
    
    router.post(endpoint, {}, {
      onSuccess: () => {
        // Handle success if needed
      },
      onError: (errors) => {
        console.error('Toggle status failed:', errors);
      },
    });
  };

  const columns = [
    {
      key: 'name',
      label: 'Name',
      render: (value: string, row: ApiKey) => (
        <div className="flex items-center">
          <KeyIcon className="h-5 w-5 text-gray-400 mr-3" />
          <div>
            <div className="text-sm font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{row.key_prefix}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'user',
      label: 'User',
      render: (user: ApiKey['user']) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{user.name}</div>
          <div className="text-sm text-gray-500">{user.email}</div>
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value: boolean, row: ApiKey) => {
        if (!value) {
          return (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              <XCircleIcon className="h-3 w-3 mr-1" />
              Revoked
            </span>
          );
        }
        
        if (row.expires_at && new Date(row.expires_at) < new Date()) {
          return (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              <ClockIcon className="h-3 w-3 mr-1" />
              Expired
            </span>
          );
        }
        
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Active
          </span>
        );
      },
    },
    {
      key: 'request_logs_count',
      label: 'Requests',
      render: (value: number) => (
        <span className="text-sm text-gray-900">{value.toLocaleString()}</span>
      ),
    },
    {
      key: 'last_used_at',
      label: 'Last Used',
      render: (value: string | null) => (
        <span className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'Never'}
        </span>
      ),
    },
    {
      key: 'expires_at',
      label: 'Expires',
      render: (value: string | null) => (
        <span className="text-sm text-gray-500">
          {value ? new Date(value).toLocaleDateString() : 'Never'}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value: string) => (
        <span className="text-sm text-gray-500">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: ApiKey) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/admin/api-keys/${row.id}`}
            className="text-blue-600 hover:text-blue-900"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </Link>
          <Link
            href={`/admin/api-keys/${row.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </Link>
          <button
            onClick={() => handleToggleStatus(row)}
            className={`${
              row.is_active 
                ? 'text-yellow-600 hover:text-yellow-900' 
                : 'text-green-600 hover:text-green-900'
            }`}
            title={row.is_active ? 'Revoke' : 'Activate'}
          >
            {row.is_active ? <XCircleIcon className="h-4 w-4" /> : <CheckCircleIcon className="h-4 w-4" />}
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { name: 'API Keys', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              API Keys
            </h1>
          </div>
          <Link
            href="/admin/api-keys/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create API Key
          </Link>
        </div>
      }
    >
      <Head title="API Keys" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <DataTable
            columns={columns}
            data={apiKeys}
            searchPlaceholder="Search API keys..."
          />
        </div>
      </div>
    </AdminLayout>
  );
}
