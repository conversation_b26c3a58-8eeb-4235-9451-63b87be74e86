import React from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  ArrowLeftIcon,
  KeyIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  is_active: boolean;
  expires_at: string | null;
  revoked_at: string | null;
  last_used_at: string | null;
  created_at: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  request_logs: Array<{
    id: string;
    endpoint: string;
    method: string;
    tokens_used: number;
    cost: number;
    status_code: number;
    created_at: string;
    llm_model: {
      name: string;
      provider: {
        name: string;
      };
    };
  }>;
}

interface UsageStats {
  total_requests: number;
  total_tokens: number;
  total_cost: number;
  requests_last_30_days: number;
  avg_tokens_per_request: number;
  most_used_model: {
    name: string;
  } | null;
}

interface ShowApiKeyProps {
  apiKey: ApiKey;
  usageStats: UsageStats;
}

export default function ShowApiKey({ apiKey, usageStats }: ShowApiKeyProps) {
  const handleToggleStatus = () => {
    const action = apiKey.is_active ? 'revoke' : 'activate';
    const endpoint = `/admin/api-keys/${apiKey.id}/${action}`;
    
    router.post(endpoint, {}, {
      onSuccess: () => {
        // Handle success if needed
      },
      onError: (errors) => {
        console.error('Toggle status failed:', errors);
      },
    });
  };

  const getStatusBadge = () => {
    if (!apiKey.is_active || apiKey.revoked_at) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <XCircleIcon className="h-3 w-3 mr-1" />
          Revoked
        </span>
      );
    }
    
    if (apiKey.expires_at && new Date(apiKey.expires_at) < new Date()) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <ClockIcon className="h-3 w-3 mr-1" />
          Expired
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <CheckCircleIcon className="h-3 w-3 mr-1" />
        Active
      </span>
    );
  };

  const breadcrumbItems = [
    { name: 'API Keys', href: '/admin/api-keys' },
    { name: apiKey.name, current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/api-keys"
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div className="flex-1">
            <Breadcrumb items={breadcrumbItems} />
            <div className="mt-2 flex items-center justify-between">
              <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                {apiKey.name}
              </h1>
              <div className="flex items-center space-x-3">
                {getStatusBadge()}
                <button
                  onClick={handleToggleStatus}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${
                    apiKey.is_active
                      ? 'text-red-700 bg-red-100 hover:bg-red-200'
                      : 'text-green-700 bg-green-100 hover:bg-green-200'
                  }`}
                >
                  {apiKey.is_active ? 'Revoke' : 'Activate'}
                </button>
                <Link
                  href={`/admin/api-keys/${apiKey.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Edit
                </Link>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <Head title={`API Key: ${apiKey.name}`} />

      <div className="space-y-6">
        {/* API Key Details */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">API Key Details</h3>
          </div>
          <div className="px-6 py-4">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">Key Prefix</dt>
                <dd className="mt-1 text-sm text-gray-900 font-mono">{apiKey.key_prefix}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">User</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  <Link
                    href={`/admin/users/${apiKey.user.id}`}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    {apiKey.user.name} ({apiKey.user.email})
                  </Link>
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {new Date(apiKey.created_at).toLocaleString()}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Last Used</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {apiKey.last_used_at ? new Date(apiKey.last_used_at).toLocaleString() : 'Never'}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Expires</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {apiKey.expires_at ? new Date(apiKey.expires_at).toLocaleString() : 'Never'}
                </dd>
              </div>
              {apiKey.revoked_at && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Revoked</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    {new Date(apiKey.revoked_at).toLocaleString()}
                  </dd>
                </div>
              )}
            </dl>
          </div>
        </div>

        {/* Usage Statistics */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Usage Statistics</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <ChartBarIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                        <dd className="text-lg font-medium text-gray-900">{usageStats.total_requests.toLocaleString()}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Cost</dt>
                        <dd className="text-lg font-medium text-gray-900">${usageStats.total_cost.toFixed(4)}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <CalendarIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Last 30 Days</dt>
                        <dd className="text-lg font-medium text-gray-900">{usageStats.requests_last_30_days.toLocaleString()}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <KeyIcon className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Avg Tokens</dt>
                        <dd className="text-lg font-medium text-gray-900">{Math.round(usageStats.avg_tokens_per_request || 0)}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Requests */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Requests</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Endpoint
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Model
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tokens
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {apiKey.request_logs.map((log) => (
                  <tr key={log.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {log.method} {log.endpoint}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.llm_model.name}
                      <div className="text-xs text-gray-400">{log.llm_model.provider.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.tokens_used.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ${log.cost.toFixed(4)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        log.status_code >= 200 && log.status_code < 300
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {log.status_code}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(log.created_at).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
