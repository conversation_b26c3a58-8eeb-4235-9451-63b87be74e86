import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface User {
  id: string;
  name: string;
  email: string;
}

interface ApiKey {
  id: string;
  name: string;
  key_prefix: string;
  expires_at: string | null;
  is_active: boolean;
  permissions: string[];
  user: User;
}

interface EditApiKeyProps {
  apiKey: ApiKey;
  users: User[];
}

interface FormData {
  name: string;
  expires_at: string;
  is_active: boolean;
  permissions: string[];
}

export default function EditApiKey({ apiKey, users }: EditApiKeyProps) {
  const { data, setData, put, processing, errors } = useForm<FormData>({
    name: apiKey.name,
    expires_at: apiKey.expires_at ? apiKey.expires_at.slice(0, 16) : '', // Format for datetime-local
    is_active: apiKey.is_active,
    permissions: apiKey.permissions || [],
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/api-keys/${apiKey.id}`);
  };

  const breadcrumbItems = [
    { name: 'API Keys', href: '/admin/api-keys' },
    { name: apiKey.name, href: `/admin/api-keys/${apiKey.id}` },
    { name: 'Edit', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex items-center space-x-4">
          <Link
            href={`/admin/api-keys/${apiKey.id}`}
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </Link>
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Edit API Key: {apiKey.name}
            </h1>
          </div>
        </div>
      }
    >
      <Head title={`Edit API Key: ${apiKey.name}`} />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* API Key Information */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">API Key Information</h3>
              <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Key Prefix</dt>
                  <dd className="text-sm text-gray-900 font-mono">{apiKey.key_prefix}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">User</dt>
                  <dd className="text-sm text-gray-900">
                    {apiKey.user.name} ({apiKey.user.email})
                  </dd>
                </div>
              </dl>
            </div>

            {/* API Key Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                API Key Name
              </label>
              <input
                type="text"
                id="name"
                value={data.name}
                onChange={(e) => setData('name', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Expiration Date */}
            <div>
              <label htmlFor="expires_at" className="block text-sm font-medium text-gray-700">
                Expiration Date (Optional)
              </label>
              <input
                type="datetime-local"
                id="expires_at"
                value={data.expires_at}
                onChange={(e) => setData('expires_at', e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-sm text-gray-500">
                Leave empty for a key that never expires.
              </p>
              {errors.expires_at && (
                <p className="mt-1 text-sm text-red-600">{errors.expires_at}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <div className="flex items-center">
                <input
                  id="is_active"
                  type="checkbox"
                  checked={data.is_active}
                  onChange={(e) => setData('is_active', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-3 block text-sm text-gray-700">
                  <span className="font-medium">Active</span>
                  <span className="block text-xs text-gray-500">
                    Uncheck to temporarily disable this API key
                  </span>
                </label>
              </div>
              {errors.is_active && (
                <p className="mt-1 text-sm text-red-600">{errors.is_active}</p>
              )}
            </div>

            {/* Warning Notice */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Important Note
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      Changes to this API key will take effect immediately. The user's model access permissions 
                      are determined by their group memberships and cannot be changed here.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3">
              <Link
                href={`/admin/api-keys/${apiKey.id}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Updating...' : 'Update API Key'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
