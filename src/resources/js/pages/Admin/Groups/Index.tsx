import React from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import DataTable from '@/Components/Admin/DataTable';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserGroupIcon,
  CpuChipIcon,
} from '@heroicons/react/24/outline';

interface Group {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  users_count: number;
  llm_models_count: number;
  created_at: string;
  permissions: Record<string, any>;
}

interface GroupsIndexProps {
  groups: Group[];
}

export default function Index({ groups }: GroupsIndexProps) {
  const handleDelete = (group: Group) => {
    if (confirm(`Are you sure you want to delete ${group.name}?`)) {
      router.delete(`/admin/groups/${group.id}`);
    }
  };

  const columns = [
    {
      key: 'name',
      label: 'Group',
      sortable: true,
      render: (value: string, row: Group) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{value}</div>
          {row.description && (
            <div className="text-sm text-gray-500">{row.description}</div>
          )}
        </div>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      sortable: true,
      render: (value: boolean) => (
        <div className="flex items-center">
          {value ? (
            <>
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-800">Active</span>
            </>
          ) : (
            <>
              <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">Inactive</span>
            </>
          )}
        </div>
      ),
    },
    {
      key: 'users_count',
      label: 'Users',
      sortable: true,
      render: (value: number) => (
        <div className="flex items-center">
          <UserGroupIcon className="h-4 w-4 text-gray-400 mr-1" />
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: 'llm_models_count',
      label: 'Models',
      sortable: true,
      render: (value: number) => (
        <div className="flex items-center">
          <CpuChipIcon className="h-4 w-4 text-gray-400 mr-1" />
          <span>{value}</span>
        </div>
      ),
    },
    {
      key: 'permissions',
      label: 'Permissions',
      render: (value: Record<string, any>) => (
        <div className="flex flex-wrap gap-1">
          {value?.admin_access && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
              Admin
            </span>
          )}
          {value?.manage_users && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
              Manage Users
            </span>
          )}
          {value?.view_analytics && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
              Analytics
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value: string) => new Date(value).toLocaleDateString(),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (_: any, row: Group) => (
        <div className="flex items-center space-x-2">
          <Link
            href={`/admin/groups/${row.id}`}
            className="text-blue-600 hover:text-blue-900"
            title="View"
          >
            <EyeIcon className="h-4 w-4" />
          </Link>
          <Link
            href={`/admin/groups/${row.id}/edit`}
            className="text-indigo-600 hover:text-indigo-900"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </Link>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      ),
    },
  ];

  const breadcrumbItems = [
    { name: 'Groups', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Groups
            </h1>
          </div>
          <Link
            href="/admin/groups/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Group
          </Link>
        </div>
      }
    >
      <Head title="Groups" />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
              <DataTable
                columns={columns}
                data={groups}
                searchPlaceholder="Search groups..."
                emptyMessage="No groups found. Create your first group to manage user permissions."
              />
        </div>
      </div>
    </AdminLayout>
  );
}
