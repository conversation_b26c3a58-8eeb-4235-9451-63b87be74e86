import React, { useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

interface Provider {
  id: string;
  name: string;
  type: string;
}

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  provider: Provider;
}

interface GroupModelPermission {
  id: string;
  can_access: boolean;
  restrictions: any[];
  llm_model: LlmModel;
}

interface Group {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  has_all_models_access: boolean;
  permissions: Record<string, any>;
  group_model_permissions: GroupModelPermission[];
}

interface EditGroupProps {
  group: Group;
  models: LlmModel[];
}

interface FormData {
  name: string;
  description: string;
  is_active: boolean;
  has_all_models_access: boolean;
  model_permissions: Array<{
    model_id: string;
    can_access: boolean;
    restrictions: string[];
  }>;
}

export default function EditGroup({ group, models }: EditGroupProps) {
  const { data, setData, put, processing, errors } = useForm<FormData>({
    name: group.name,
    description: group.description || '',
    is_active: group.is_active,
    has_all_models_access: group.has_all_models_access,
    model_permissions: group.group_model_permissions.map(permission => ({
      model_id: permission.llm_model.id,
      can_access: permission.can_access,
      restrictions: permission.restrictions || [],
    })),
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/admin/groups/${group.id}`);
  };

  const handleModelToggle = (modelId: string) => {
    const currentPermissions = [...data.model_permissions];
    const existingIndex = currentPermissions.findIndex(p => p.model_id === modelId);
    
    if (existingIndex > -1) {
      currentPermissions.splice(existingIndex, 1);
    } else {
      currentPermissions.push({
        model_id: modelId,
        can_access: true,
        restrictions: [],
      });
    }
    
    setData('model_permissions', currentPermissions);
  };

  const isModelSelected = (modelId: string) => {
    return data.model_permissions.some(p => p.model_id === modelId);
  };

  const breadcrumbItems = [
    { name: 'Groups', href: '/admin/groups' },
    { name: group.name, href: `/admin/groups/${group.id}` },
    { name: 'Edit', current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Edit Group: {group.name}
            </h1>
          </div>
          <Link
            href={`/admin/groups/${group.id}`}
            className="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Link>
        </div>
      }
    >
      <Head title={`Edit Group: ${group.name}`} />

      <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Group Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Group Name
              </label>
              <input
                type="text"
                id="name"
                value={data.name}
                onChange={(e) => setData('name', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                rows={3}
                value={data.description}
                onChange={(e) => setData('description', e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Optional description for this group"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Status */}
            <div className="flex items-center">
              <input
                id="is_active"
                type="checkbox"
                checked={data.is_active}
                onChange={(e) => setData('is_active', e.target.checked)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                Active
              </label>
            </div>

            {/* Model Access */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                Model Access
              </label>
              
              {/* All Models Access Toggle */}
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    id="has_all_models_access"
                    type="checkbox"
                    checked={data.has_all_models_access}
                    onChange={(e) => setData('has_all_models_access', e.target.checked)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="has_all_models_access" className="ml-2 block text-sm text-gray-900">
                    Grant access to all models (current and future)
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  When enabled, this group will have access to all active models without needing to configure individual permissions.
                </p>
              </div>

              {/* Individual Model Selection */}
              {!data.has_all_models_access && (
                <div>
                  <p className="text-sm text-gray-700 mb-3">
                    Select specific models this group should have access to:
                  </p>
                  <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                    {models.map((model) => (
                      <div key={model.id} className="relative flex items-start">
                        <div className="flex items-center h-5">
                          <input
                            id={`model-${model.id}`}
                            type="checkbox"
                            checked={isModelSelected(model.id)}
                            onChange={() => handleModelToggle(model.id)}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                        </div>
                        <div className="ml-3 text-sm">
                          <label htmlFor={`model-${model.id}`} className="font-medium text-gray-700">
                            {model.name}
                          </label>
                          <p className="text-gray-500">{model.model_identifier}</p>
                          <p className="text-xs text-gray-400">{model.provider.name} ({model.provider.type})</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-3 pt-6">
              <Link
                href={`/admin/groups/${group.id}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={processing}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {processing ? 'Updating...' : 'Update Group'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}
