import React from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AdminLayout from '@/Layouts/AdminLayout';
import Breadcrumb from '@/Components/Admin/Breadcrumb';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserGroupIcon,
  CpuChipIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';

interface Provider {
  id: string;
  name: string;
  type: string;
}

interface LlmModel {
  id: string;
  name: string;
  model_identifier: string;
  provider: Provider;
}

interface User {
  id: string;
  name: string;
  email: string;
}

interface GroupModelPermission {
  id: string;
  can_access: boolean;
  restrictions: any[];
  llm_model: LlmModel;
}

interface Group {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  has_all_models_access: boolean;
  permissions: Record<string, any>;
  created_at: string;
  updated_at: string;
  users: User[];
  llm_models: LlmModel[];
  group_model_permissions: GroupModelPermission[];
}

interface ShowGroupProps {
  group: Group;
}

export default function ShowGroup({ group }: ShowGroupProps) {
  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this group? This action cannot be undone.')) {
      router.delete(`/admin/groups/${group.id}`);
    }
  };

  const breadcrumbItems = [
    { name: 'Groups', href: '/admin/groups' },
    { name: group.name, current: true },
  ];

  return (
    <AdminLayout
      header={
        <div className="flex justify-between items-center w-full">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              {group.name}
            </h1>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href={`/admin/groups/${group.id}/edit`}
              className="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150"
            >
              <PencilIcon className="h-4 w-4 mr-2" />
              Edit
            </Link>
            <button
              onClick={handleDelete}
              className="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"
            >
              <TrashIcon className="h-4 w-4 mr-2" />
              Delete
            </button>
            <Link
              href="/admin/groups"
              className="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back
            </Link>
          </div>
        </div>
      }
    >
      <Head title={`Group: ${group.name}`} />

      <div className="space-y-6">
        {/* Group Details */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Group Details</h3>
          </div>
          <div className="px-6 py-4">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-500">Name</dt>
                <dd className="mt-1 text-sm text-gray-900">{group.name}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1 flex items-center">
                  {group.is_active ? (
                    <>
                      <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                      <span className="text-green-800">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                      <span className="text-red-800">Inactive</span>
                    </>
                  )}
                </dd>
              </div>
              {group.description && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500">Description</dt>
                  <dd className="mt-1 text-sm text-gray-900">{group.description}</dd>
                </div>
              )}
              <div>
                <dt className="text-sm font-medium text-gray-500">Created</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {new Date(group.created_at).toLocaleDateString()}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                <dd className="mt-1 text-sm text-gray-900">
                  {new Date(group.updated_at).toLocaleDateString()}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        {/* Users */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                <UsersIcon className="h-5 w-5 mr-2" />
                Users ({group.users.length})
              </h3>
            </div>
          </div>
          <div className="px-6 py-4">
            {group.users.length > 0 ? (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {group.users.map((user) => (
                  <div key={user.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-8 w-8 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          <Link
                            href={`/admin/users/${user.id}`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            {user.name}
                          </Link>
                        </p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No users assigned to this group.</p>
            )}
          </div>
        </div>

        {/* Model Access */}
        <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
              <CpuChipIcon className="h-5 w-5 mr-2" />
              Model Access
            </h3>
          </div>
          <div className="px-6 py-4">
            {group.has_all_models_access ? (
              <div className="text-center py-4">
                <CpuChipIcon className="mx-auto h-12 w-12 text-green-500" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">All Models Access</h3>
                <p className="mt-1 text-sm text-gray-500">
                  This group has access to all active models in the system.
                </p>
              </div>
            ) : group.llm_models.length > 0 ? (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {group.llm_models.map((model) => (
                  <div key={model.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center">
                      <CpuChipIcon className="h-8 w-8 text-blue-500 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{model.name}</p>
                        <p className="text-sm text-gray-500">{model.model_identifier}</p>
                        <p className="text-xs text-gray-400">{model.provider.name}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No specific model access configured.</p>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
