import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import UserLayout from '@/layouts/UserLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Key, ArrowLeft, Info, Users } from 'lucide-react';

interface Group {
    id: string;
    name: string;
    description?: string;
}

interface Props {
    groups: Group[];
}

export default function CreateApiKey({ groups = [] }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        expires_at: '',
        group_id: '',
    });

    const breadcrumbItems = [
        { name: 'Dashboard', href: '/dashboard' },
        { name: 'API Keys', href: '/dashboard/api-keys' },
        { name: 'Create', current: true },
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/dashboard/api-keys');
    };

    return (
        <UserLayout breadcrumbs={breadcrumbItems}>
            <Head title="Create API Key" />

            <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <Link href="/dashboard/api-keys">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to API Keys
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold">Create API Key</h1>
                        <p className="text-muted-foreground">
                            Create a new API key for accessing the LLM router
                        </p>
                    </div>
                </div>

                <div className="grid gap-6 max-w-2xl">
                    {/* Information Alert */}
                    <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                            <div className="space-y-2">
                                <p className="font-semibold">Important Security Information</p>
                                <ul className="text-sm space-y-1 list-disc list-inside">
                                    <li>Your API key will be shown only once after creation</li>
                                    <li>Store it securely and never share it publicly</li>
                                    <li>Use environment variables in your applications</li>
                                    <li>You can regenerate or delete keys at any time</li>
                                </ul>
                            </div>
                        </AlertDescription>
                    </Alert>

                    {/* Create Form */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Key className="h-5 w-5" />
                                API Key Details
                            </CardTitle>
                            <CardDescription>
                                Provide a name and optional expiration date for your API key
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="space-y-2">
                                    <Label htmlFor="name">API Key Name *</Label>
                                    <Input
                                        id="name"
                                        type="text"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., Production App, Development, Mobile App"
                                        className={errors.name ? 'border-destructive' : ''}
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                    <p className="text-sm text-muted-foreground">
                                        Choose a descriptive name to help you identify this key later
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
                                    <Input
                                        id="expires_at"
                                        type="date"
                                        value={data.expires_at}
                                        onChange={(e) => setData('expires_at', e.target.value)}
                                        min={new Date().toISOString().split('T')[0]}
                                        className={errors.expires_at ? 'border-destructive' : ''}
                                    />
                                    {errors.expires_at && (
                                        <p className="text-sm text-destructive">{errors.expires_at}</p>
                                    )}
                                    <p className="text-sm text-muted-foreground">
                                        Leave empty for a key that never expires. You can always delete or regenerate keys manually.
                                    </p>
                                </div>

                                {groups.length > 1 && (
                                    <div className="space-y-2">
                                        <Label htmlFor="group_id">Assign to Group (Optional)</Label>
                                        <Select value={data.group_id} onValueChange={(value) => setData('group_id', value)}>
                                            <SelectTrigger className={errors.group_id ? 'border-destructive' : ''}>
                                                <SelectValue placeholder="Select a group or leave unassigned" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="">No specific group (access all your models)</SelectItem>
                                                {groups.map((group) => (
                                                    <SelectItem key={group.id} value={group.id}>
                                                        <div className="flex items-center gap-2">
                                                            <Users className="h-4 w-4" />
                                                            <span>{group.name}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.group_id && (
                                            <p className="text-sm text-destructive">{errors.group_id}</p>
                                        )}
                                        <p className="text-sm text-muted-foreground">
                                            Assign this API key to a specific group to limit model access. Leave unassigned to access all models you have permission for.
                                        </p>
                                    </div>
                                )}

                                <div className="flex items-center gap-3 pt-4">
                                    <Button type="submit" disabled={processing}>
                                        {processing ? (
                                            <>
                                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                                Creating...
                                            </>
                                        ) : (
                                            <>
                                                <Key className="mr-2 h-4 w-4" />
                                                Create API Key
                                            </>
                                        )}
                                    </Button>
                                    <Button type="button" variant="outline" asChild>
                                        <Link href="/dashboard/api-keys">Cancel</Link>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>

                    {/* Usage Instructions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>How to Use Your API Key</CardTitle>
                            <CardDescription>
                                Once created, use your API key to authenticate requests
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-semibold mb-2">HTTP Header</h4>
                                <div className="bg-muted p-3 rounded font-mono text-sm">
                                    Authorization: Bearer YOUR_API_KEY
                                </div>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Example cURL Request</h4>
                                <div className="bg-muted p-3 rounded font-mono text-sm whitespace-pre-wrap">
{`curl -X POST https://your-domain.com/v1/chat/completions \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'`}
                                </div>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Environment Variable (Recommended)</h4>
                                <div className="bg-muted p-3 rounded font-mono text-sm">
                                    export LLM_ROUTER_API_KEY="YOUR_API_KEY"
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </UserLayout>
    );
}
